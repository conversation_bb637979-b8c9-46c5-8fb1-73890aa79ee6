#!/bin/bash
set -e

echo "=== Container startup debug info ==="
echo "NODE_ENV: $NODE_ENV"
echo "POSTGRES_HOST: $POSTGRES_HOST"
echo "POSTGRES_PORT: $POSTGRES_PORT"
echo "POSTGRES_DB: $POSTGRES_DB"
echo "SERVER_TCP_PORT: $SERVER_TCP_PORT"

echo "=== Testing database connectivity ==="
if command -v nc >/dev/null 2>&1; then
    nc -z "$POSTGRES_HOST" "$POSTGRES_PORT" && echo "✅ Database reachable" || echo "❌ Database unreachable"
else
    echo "netcat not available, skipping connectivity test"
fi

echo "=== Starting application ==="
exec "$@"
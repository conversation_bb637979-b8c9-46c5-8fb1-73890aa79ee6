import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModuleAsyncOptions } from '@nestjs/typeorm';

export const typeOrmConfigAsync: TypeOrmModuleAsyncOptions = {
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => {
    const isProd = configService.get<string>('NODE_ENV') === 'production';
    const host = configService.get<string>('POSTGRES_HOST')!;
    const port = configService.get<number>('POSTGRES_PORT')!;
    const database = configService.get<string>('POSTGRES_DB')!;

    console.log(`🔗 Configuring database connection to ${host}:${port}/${database}`);

    return {
      type: 'postgres',
      username: configService.get<string>('POSTGRES_USER')!,
      password: configService.get<string>('POSTGRES_PASSWORD')!,
      database,
      host,
      port,
      retryAttempts: 10, // Increased retry attempts
      retryDelay: 3000, // 3 seconds between retries
      timeout: 60000, // 60 seconds timeout
      connectTimeoutMS: 60000, // 60 seconds connection timeout
      logging: !isProd,
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      migrations: [__dirname + '/../migrations/*{.ts,.js}'],
      // Additional connection options for better reliability
      extra: {
        connectionTimeoutMillis: 60000,
        idleTimeoutMillis: 30000,
        max: 10, // Maximum number of connections in pool
        min: 1, // Minimum number of connections in pool
      },
    };
  },
  inject: [ConfigService],
};

import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModuleAsyncOptions } from '@nestjs/typeorm';

// Helper function to test DNS resolution
async function testDnsResolution(hostname: string): Promise<boolean> {
  return new Promise((resolve) => {
    const dns = require('dns');
    dns.lookup(hostname, (err: any) => {
      if (err) {
        console.log(`❌ DNS resolution failed for ${hostname}:`, err.message);
        resolve(false);
      } else {
        console.log(`✅ DNS resolution successful for ${hostname}`);
        resolve(true);
      }
    });
  });
}

export const typeOrmConfigAsync: TypeOrmModuleAsyncOptions = {
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => {
    const isProd = configService.get<string>('NODE_ENV') === 'production';
    let host = configService.get<string>('POSTGRES_HOST')!;
    let port = configService.get<number>('POSTGRES_PORT')!;
    const database = configService.get<string>('POSTGRES_DB')!;

    console.log(`📊 Raw environment variables:`);
    console.log(`  POSTGRES_HOST: "${configService.get<string>('POSTGRES_HOST')}"`);
    console.log(`  POSTGRES_PORT: "${configService.get<string>('POSTGRES_PORT')}"`);
    console.log(`  POSTGRES_DB: "${configService.get<string>('POSTGRES_DB')}"`);
    console.log(`  POSTGRES_USER: "${configService.get<string>('POSTGRES_USER')}"`);
    console.log(`  NODE_ENV: "${configService.get<string>('NODE_ENV')}"`);
    console.log(`  ENVIRONMENT: "${configService.get<string>('ENVIRONMENT')}"`);

    // Handle case where host already includes port
    if (host && host.includes(':')) {
      console.log(`⚠️ POSTGRES_HOST contains port: ${host}`);
      const hostParts = host.split(':');
      host = hostParts[0]!;
      const hostPort = parseInt(hostParts[1]!);
      if (!isNaN(hostPort)) {
        port = hostPort;
        console.log(`✅ Extracted host: ${host}, port: ${port}`);
      } else {
        console.log(`❌ Invalid port in POSTGRES_HOST, using POSTGRES_PORT: ${port}`);
      }
    }

    const useSSL = host.includes('rds.amazonaws.com');
    console.log(`🔗 Final database connection config: ${host}:${port}/${database}`);
    console.log(`🔒 SSL enabled: ${useSSL}`);

    // Test DNS resolution before attempting connection
    console.log(`🔍 Testing DNS resolution for ${host}...`);
    const dnsResolved = await testDnsResolution(host);
    if (!dnsResolved) {
      console.log(`⚠️ DNS resolution failed, but TypeORM will still attempt to connect with retries`);
    }

    return {
      type: 'postgres',
      username: configService.get<string>('POSTGRES_USER')!,
      password: configService.get<string>('POSTGRES_PASSWORD')!,
      database,
      host,
      port,
      retryAttempts: 10, // Increased retry attempts
      retryDelay: 3000, // 3 seconds between retries
      timeout: 60000, // 60 seconds timeout
      connectTimeoutMS: 60000, // 60 seconds connection timeout
      logging: !isProd,
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      migrations: [__dirname + '/../migrations/*{.ts,.js}'],
      // SSL configuration for RDS (always use SSL for RDS connections)
      ssl: host.includes('rds.amazonaws.com') ? true : false,

      // Additional connection options for better reliability
      extra: {
        connectionTimeoutMillis: 60000,
        idleTimeoutMillis: 30000,
        max: 10, // Maximum number of connections in pool
        min: 1, // Minimum number of connections in pool
        // Additional options to help with DNS resolution
        keepAlive: true,
        keepAliveInitialDelayMillis: 0,
        // SSL options for the connection pool
        ssl: host.includes('rds.amazonaws.com') ? 'require' : false,
      },
    };
  },
  inject: [ConfigService],
};
